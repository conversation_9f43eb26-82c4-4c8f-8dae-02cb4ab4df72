import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Github,
  Twitter,
  Linkedin,
  Mail,
  MapPin,
  Phone,
  BookOpen,
  Users,
  MessageSquare,
  Heart,
  ArrowUp,
} from "lucide-react";

const Footer = () => {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  return (
    <footer className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="absolute top-0 right-0 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10"></div>
      <div className="absolute bottom-0 left-0 w-96 h-96 bg-indigo-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10"></div>

      <div className="relative">
        {/* Main Footer Content */}
        <div className="container mx-auto px-6 py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Brand Section */}
            <div className="lg:col-span-1">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-xl">M</span>
                </div>
                <div>
                  <h3 className="text-2xl font-bold">
                    <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-indigo-400">
                      MERN
                    </span>
                    <span className="text-white ml-1">Ecosystem</span>
                  </h3>
                  <p className="text-gray-400 text-sm">Developer Community</p>
                </div>
              </div>

              <p className="text-gray-300 mb-6 leading-relaxed">
                Join thousands of developers learning MongoDB, Express.js,
                React, and Node.js. Share knowledge, discover best practices,
                and build amazing web applications.
              </p>

              {/* Social Links */}
              <div className="flex space-x-4">
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-10 h-10 bg-gray-800 hover:bg-blue-600 text-gray-300 hover:text-white transition-all duration-300 rounded-lg"
                >
                  <Github className="w-5 h-5" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-10 h-10 bg-gray-800 hover:bg-blue-400 text-gray-300 hover:text-white transition-all duration-300 rounded-lg"
                >
                  <Twitter className="w-5 h-5" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-10 h-10 bg-gray-800 hover:bg-blue-700 text-gray-300 hover:text-white transition-all duration-300 rounded-lg"
                >
                  <Linkedin className="w-5 h-5" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-10 h-10 bg-gray-800 hover:bg-red-600 text-gray-300 hover:text-white transition-all duration-300 rounded-lg"
                >
                  <Mail className="w-5 h-5" />
                </Button>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="text-lg font-semibold mb-6 text-white">
                Quick Links
              </h4>
              <ul className="space-y-3">
                <li>
                  <Link
                    to="/articles"
                    className="flex items-center text-gray-300 hover:text-blue-400 transition-colors duration-300 group"
                  >
                    <BookOpen className="w-4 h-4 mr-2 group-hover:text-blue-400" />
                    Articles & Tutorials
                  </Link>
                </li>
                <li>
                  <Link
                    to="/authors"
                    className="flex items-center text-gray-300 hover:text-blue-400 transition-colors duration-300 group"
                  >
                    <Users className="w-4 h-4 mr-2 group-hover:text-blue-400" />
                    Expert Authors
                  </Link>
                </li>
                <li>
                  <Link
                    to="#"
                    className="flex items-center text-gray-300 hover:text-blue-400 transition-colors duration-300 group"
                  >
                    <MessageSquare className="w-4 h-4 mr-2 group-hover:text-blue-400" />
                    Community
                  </Link>
                </li>
                <li>
                  <Link
                    to="#"
                    className="text-gray-300 hover:text-blue-400 transition-colors duration-300"
                  >
                    About Us
                  </Link>
                </li>
                <li>
                  <Link
                    to="#"
                    className="text-gray-300 hover:text-blue-400 transition-colors duration-300"
                  >
                    Contact
                  </Link>
                </li>
              </ul>
            </div>

            {/* Resources */}
            <div>
              <h4 className="text-lg font-semibold mb-6 text-white">
                Resources
              </h4>
              <ul className="space-y-3">
                <li>
                  <a
                    href="#"
                    className="text-gray-300 hover:text-blue-400 transition-colors duration-300"
                  >
                    Getting Started
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-300 hover:text-blue-400 transition-colors duration-300"
                  >
                    Documentation
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-300 hover:text-blue-400 transition-colors duration-300"
                  >
                    Best Practices
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-300 hover:text-blue-400 transition-colors duration-300"
                  >
                    Code Examples
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-300 hover:text-blue-400 transition-colors duration-300"
                  >
                    FAQ
                  </a>
                </li>
              </ul>
            </div>

            {/* Newsletter */}
            <div>
              <h4 className="text-lg font-semibold mb-6 text-white">
                Stay Updated
              </h4>
              <p className="text-gray-300 mb-4">
                Get the latest articles and tutorials delivered to your inbox.
              </p>

              <div className="space-y-3">
                <div className="flex">
                  <Input
                    type="email"
                    placeholder="Enter your email"
                    className="bg-gray-800 border-gray-700 text-white placeholder-gray-400 focus:border-blue-500 rounded-r-none"
                  />
                  <Button className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 rounded-l-none px-6">
                    Subscribe
                  </Button>
                </div>
                <p className="text-xs text-gray-400">
                  No spam, unsubscribe at any time.
                </p>
              </div>

              {/* Contact Info */}
              <div className="mt-8 space-y-3">
                <div className="flex items-center text-gray-300">
                  <MapPin className="w-4 h-4 mr-3 text-blue-400" />
                  <span className="text-sm">San Francisco, CA</span>
                </div>
                <div className="flex items-center text-gray-300">
                  <Phone className="w-4 h-4 mr-3 text-blue-400" />
                  <span className="text-sm">+****************</span>
                </div>
                <div className="flex items-center text-gray-300">
                  <Mail className="w-4 h-4 mr-3 text-blue-400" />
                  <span className="text-sm"><EMAIL></span>
                </div>
              </div>
            </div>
          </div>

          {/* Stats Section */}
          <div className="border-t border-gray-700 mt-12 pt-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
              <div>
                <div className="text-3xl font-bold text-blue-400 mb-2">
                  500+
                </div>
                <div className="text-gray-400 text-sm">Articles Published</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-green-400 mb-2">
                  50+
                </div>
                <div className="text-gray-400 text-sm">Expert Authors</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-purple-400 mb-2">
                  10k+
                </div>
                <div className="text-gray-400 text-sm">Active Developers</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-yellow-400 mb-2">
                  25k+
                </div>
                <div className="text-gray-400 text-sm">Monthly Readers</div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-700 bg-gray-900/50">
          <div className="container mx-auto px-6 py-6">
            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
              <div className="flex items-center space-x-6 text-sm text-gray-400">
                <span>© 2024 MERN Ecosystem. All rights reserved.</span>
                <div className="hidden md:flex items-center space-x-4">
                  <a href="#" className="hover:text-blue-400 transition-colors">
                    Privacy Policy
                  </a>
                  <span>•</span>
                  <a href="#" className="hover:text-blue-400 transition-colors">
                    Terms of Service
                  </a>
                  <span>•</span>
                  <a href="#" className="hover:text-blue-400 transition-colors">
                    Cookie Policy
                  </a>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex items-center text-sm text-gray-400">
                  <span>Made with</span>
                  <Heart className="w-4 h-4 mx-1 text-red-500 animate-pulse" />
                  <span>by developers, for developers</span>
                </div>

                <Button
                  onClick={scrollToTop}
                  variant="ghost"
                  size="icon"
                  className="w-10 h-10 bg-gray-800 hover:bg-blue-600 text-gray-300 hover:text-white transition-all duration-300 rounded-lg"
                >
                  <ArrowUp className="w-5 h-5" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
